<template>
  <div class="weather-exam-result">
    <!-- 考试结果头部信息 -->
    <div class="result-header">
      <div class="header-content">
        <div class="result-title">
          <h2>{{ examData.title || '历史个例考试结果' }}</h2>
          <div class="exam-meta">
            <div class="meta-item">
              <i class="el-icon-time" />
              <span>考试时长：{{ examData.duration || examData.totalTime || 60 }}分钟</span>
            </div>
            <div class="meta-item">
              <i class="el-icon-star-on" />
              <span>总分：{{ examData.totalScore || 100 }}分</span>
            </div>
            <div class="meta-item score-display">
              <i class="el-icon-trophy" />
              <span>您的得分：</span>
              <span class="final-score">{{ resultData.totalScore || 0 }}</span>
              <span>分</span>
            </div>
          </div>
        </div>

        <!-- 得分统计 -->
        <div class="score-summary">
          <div class="score-card">
            <div class="score-item">
              <div class="score-label">总得分</div>
              <div class="score-value primary">{{ resultData.totalScore || 0 }}</div>
              <div class="score-unit">分</div>
            </div>
            <div class="score-divider" />
            <div class="score-item">
              <div class="score-label">正确率</div>
              <div class="score-value success">{{ calculateAccuracy() }}%</div>
            </div>
            <div class="score-divider" />
            <div class="score-item">
              <div class="score-label">排名</div>
              <div class="score-value info">{{ resultData.ranking || '--' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 考试内容结果 -->
    <div class="result-content">
      <!-- 第一部分：降水分级落区预报结果 -->
      <el-card class="result-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第一部分：降水分级落区预报</h3>
            <div class="section-score">
              <el-tag type="primary" size="medium">
                得分：{{ resultData.precipitationScore || 0 }}/40分
              </el-tag>
            </div>
          </div>
        </div>

        <div class="precipitation-result-section">
          <!-- 题目信息 -->
          <div class="question-info">
            <div class="question-content">
              <h4>题目要求：</h4>
              <p>{{ questionData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。' }}</p>
            </div>
            <div class="region-info">
              <h4>预报区域：</h4>
              <el-tag type="success" size="medium">{{ getRegionLabel(questionData.precipitationRegion || 'region1') }}</el-tag>
            </div>
          </div>

          <!-- 降水落区结果展示 -->
          <div class="precipitation-result-display">
            <div class="result-tabs">
              <el-tabs v-model="precipitationActiveTab" class="result-tabs-container">
                <el-tab-pane label="您的答案" name="user">
                  <div class="precipitation-display-container">
                    <precipitation-drawing
                      ref="userPrecipitationDrawing"
                      :initial-data="userPrecipitationData"
                      :region="questionData.precipitationRegion || 'region1'"
                      :readonly="true"
                      :show-legend="true"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="标准答案" name="standard">
                  <div class="precipitation-display-container">
                    <precipitation-drawing
                      ref="standardPrecipitationDrawing"
                      :initial-data="standardPrecipitationData"
                      :region="questionData.precipitationRegion || 'region1'"
                      :readonly="true"
                      :show-legend="true"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="对比分析" name="comparison">
                  <div class="comparison-analysis">
                    <div class="analysis-summary">
                      <h4>评分分析</h4>
                      <div class="analysis-items">
                        <div class="analysis-item">
                          <span class="analysis-label">绘制完整性：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.completeness || '良好' }}</span>
                        </div>
                        <div class="analysis-item">
                          <span class="analysis-label">区域准确性：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.accuracy || '较好' }}</span>
                        </div>
                        <div class="analysis-item">
                          <span class="analysis-label">等级划分：</span>
                          <span class="analysis-value">{{ precipitationAnalysis.grading || '基本正确' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 第二部分：灾害性天气预报结果 -->
      <el-card class="result-section" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>第二部分：灾害性天气预报</h3>
            <div class="section-score">
              <el-tag type="success" size="medium">
                得分：{{ resultData.weatherScore || 0 }}/60分
              </el-tag>
            </div>
          </div>
        </div>

        <div class="weather-result-section">
          <!-- 天气预报结果表格 -->
          <div class="result-table-container">
            <weather-result-table
              ref="weatherResultTable"
              :question="questionData"
              :user-answers="userWeatherAnswers"
              :standard-answers="standardWeatherAnswers"
              :scoring-result="weatherScoringResult"
              :readonly="true"
            />
          </div>
        </div>
      </el-card>

      <!-- 详细评分信息 -->
      <el-card class="result-section scoring-details" shadow="hover">
        <div slot="header" class="section-header">
          <div class="section-title">
            <h3>详细评分信息</h3>
          </div>
        </div>

        <div class="scoring-breakdown">
          <div class="scoring-grid">
            <!-- 第一部分评分详情 -->
            <div class="scoring-part">
              <h4>第一部分 - 降水分级落区预报</h4>

              <!-- 降水落区详细评分信息 -->
              <div v-if="precipitationAreaDetails" class="precipitation-area-details">
                <div class="detail-section">
                  <h5>降水落区评分详情</h5>
                  <div class="detail-grid">
                    <div class="detail-item">
                      <span class="detail-label">总得分：</span>
                      <span class="detail-value score-highlight">{{ formatScoreToTwoDecimals(precipitationAreaDetails.score) }}分</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">总站点数：</span>
                      <span class="detail-value">{{ precipitationAreaDetails.totalStations || 0 }}个</span>
                    </div>
                  </div>
                </div>

                <!-- TS评分详情 -->
                <div v-if="precipitationAreaDetails.studentTSScores" class="ts-scores-section">
                  <h5>TS评分详情</h5>
                  <div class="ts-comparison">
                    <div class="ts-column">
                      <h6>我的TS评分</h6>
                      <div class="ts-items">
                        <div
                          v-for="level in getSortedTSLevels(precipitationAreaDetails.studentTSScores)"
                          :key="'student-' + level"
                          class="ts-item"
                        >
                          <span class="ts-level">{{ level }}：</span>
                          <span class="ts-score">{{ formatScore(precipitationAreaDetails.studentTSScores[level]) }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="ts-column">
                      <h6>CMA-MESO TS评分</h6>
                      <div class="ts-items">
                        <div
                          v-for="level in getSortedTSLevels(precipitationAreaDetails.cmaMesoTSScores)"
                          :key="'cma-' + level"
                          class="ts-item"
                        >
                          <span class="ts-level">{{ level }}：</span>
                          <span class="ts-score">{{ formatScore(precipitationAreaDetails.cmaMesoTSScores[level]) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 基础评分和技能评分 -->
                <div class="base-skill-scores">
                  <div v-if="precipitationAreaDetails.baseScores" class="score-column">
                    <h6>基础评分</h6>
                    <div class="score-items">
                      <div
                        v-for="level in getSortedTSLevels(precipitationAreaDetails.baseScores)"
                        :key="'base-' + level"
                        class="score-item-small"
                      >
                        <span class="score-level">{{ level }}：</span>
                        <span class="score-value-small">{{ formatScore(precipitationAreaDetails.baseScores[level]) }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-if="precipitationAreaDetails.skillScores" class="score-column">
                    <h6>技能评分</h6>
                    <div class="score-items">
                      <div
                        v-for="level in getSortedTSLevels(precipitationAreaDetails.skillScores)"
                        :key="'skill-' + level"
                        class="score-item-small"
                      >
                        <span class="score-level">{{ level }}：</span>
                        <span class="score-value-small">{{ formatScore(precipitationAreaDetails.skillScores[level]) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 评分总结 -->
                <div v-if="precipitationAreaDetails.summary" class="scoring-summary">
                  <h6>评分总结</h6>
                  <p class="summary-text">{{ precipitationAreaDetails.summary }}</p>
                </div>

              </div>

              <!-- 传统评分项目（作为备用显示） -->
              <div v-else class="scoring-items">
                <div class="scoring-item">
                  <span class="item-name">降水区域绘制</span>
                  <span class="item-score">{{ precipitationScoring.areaScore || 0 }}/20分</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">等级划分准确性</span>
                  <span class="item-score">{{ precipitationScoring.levelScore || 0 }}/15分</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">区域完整性</span>
                  <span class="item-score">{{ precipitationScoring.completenessScore || 0 }}/5分</span>
                </div>
              </div>
            </div>

            <!-- 第二部分评分详情 -->
            <div class="scoring-part">
              <h4>第二部分 - 灾害性天气预报（单站总分10分）</h4>

              <!-- 评分要素说明 -->
              <div class="scoring-elements-info">
                <div class="elements-grid">
                  <div class="element-info">
                    <span class="element-name">08-08最大风力(级)</span>
                    <span class="element-score">1.0分</span>
                  </div>
                  <div class="element-info">
                    <span class="element-name">08-08最大风力时的风向</span>
                    <span class="element-score">1.0分</span>
                  </div>
                  <div class="element-info">
                    <span class="element-name">最低气温℃</span>
                    <span class="element-score">2.0分</span>
                  </div>
                  <div class="element-info">
                    <span class="element-name">最高气温℃</span>
                    <span class="element-score">2.0分</span>
                  </div>
                  <div class="element-info">
                    <span class="element-name">08-08降水(雨、雪)量级</span>
                    <span class="element-score">2.0分</span>
                  </div>
                  <div class="element-info">
                    <span class="element-name">灾害性天气类型</span>
                    <span class="element-score">2.0分</span>
                  </div>
                </div>
              </div>

              <!-- 实际得分情况 -->
              <div class="scoring-items">
                <div class="scoring-item">
                  <span class="item-name">风力预报 (满分1.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.windForceScore || 0) }}分</span>
                  <span class="item-detail">{{ getWindForceScoreDetail() }}</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">风向预报 (满分1.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.windDirectionScore || 0) }}分</span>
                  <span class="item-detail">{{ getWindDirectionScoreDetail() }}</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">最低气温 (满分2.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals((weatherScoring.minTemperatureScore || 0)) }}分</span>
                  <span class="item-detail">容差±2℃</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">最高气温 (满分2.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals((weatherScoring.maxTemperatureScore || 0)) }}分</span>
                  <span class="item-detail">容差±2℃</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">降水预报 (满分2.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.precipitationScore || 0) }}分</span>
                  <span class="item-detail">{{ getPrecipitationScoreDetail() }}</span>
                </div>
                <div class="scoring-item">
                  <span class="item-name">灾害天气预报 (满分2.0分)</span>
                  <span class="item-score">{{ formatScoreToTwoDecimals(weatherScoring.disasterScore || 0) }}分</span>
                  <span class="item-detail">{{ getDisasterWeatherScoreDetail() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <el-button size="large" @click="goBack">
          <i class="el-icon-back" />
          返回考试列表
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import WeatherResultTable from '@/components/weather/WeatherResultTable'
import PrecipitationDrawing from '@/components/weather/PrecipitationDrawing'
import {
  getWeatherExamResult,
  getWeatherExamDetail
} from '@/api/weather/weather'

export default {
  name: 'WeatherExamResult',
  components: {
    WeatherResultTable,
    PrecipitationDrawing
  },
  data() {
    return {
      examId: '',
      examData: {},
      questionData: {},
      resultData: {},

      // 降水预报相关数据
      userPrecipitationData: null,
      standardPrecipitationData: null,
      precipitationActiveTab: 'user',
      precipitationAnalysis: {},
      precipitationScoring: {},

      // 天气预报相关数据
      userWeatherAnswers: {},
      standardWeatherAnswers: {},
      weatherScoringResult: {},
      weatherScoring: {},

      // 题目场景数据
      scenarioData: {},

      // 降水落区详细评分数据
      precipitationAreaDetails: null,

      loading: false
    }
  },
  computed: {
    // 这里可以添加其他computed属性
  },
  created() {
    this.examId = this.$route.params.id
    this.initResultPage()
  },
  methods: {
    // 计算正确率
    calculateAccuracy() {
      const totalScore = this.examData.totalScore || 100
      const userScore = this.resultData.totalScore || 0
      return Math.round((userScore / totalScore) * 100)
    },

    // 初始化结果页面
    async initResultPage() {
      this.loading = true
      try {
        // 获取考试结果数据
        await this.loadExamResult()

        // 获取题目详情
        await this.loadQuestionData()
      } catch (error) {
        console.error('加载考试结果失败:', error)
        this.$message.error('加载考试结果失败')
      } finally {
        this.loading = false
      }
    },

    // 加载考试结果
    async loadExamResult() {
      try {
        const response = await getWeatherExamResult({ examId: this.examId })

        if (response.code === 0 && response.data) {
          const resultData = response.data

          // 调试：打印完整的返回数据结构
          console.log('完整的考试结果数据:', resultData)
          console.log('gradingDetails:', resultData.gradingDetails)
          if (resultData.gradingDetails) {
            console.log('gradingDetails keys:', Object.keys(resultData.gradingDetails))
            if (resultData.gradingDetails.detailResults) {
              console.log('detailResults:', resultData.gradingDetails.detailResults)
              console.log('detailResults keys:', Object.keys(resultData.gradingDetails.detailResults))
              if (resultData.gradingDetails.detailResults.precipitationArea) {
                console.log('precipitationArea:', resultData.gradingDetails.detailResults.precipitationArea)
              }
            }
          }

          // 解析题目场景数据
          let scenarioData = {}
          try {
            if (resultData.scenarioData) {
              scenarioData = JSON.parse(resultData.scenarioData)
            }
          } catch (e) {
            console.warn('解析scenarioData失败:', e)
          }

          // 设置考试基本信息
          this.examData = {
            title: resultData.examTitle,
            totalTime: resultData.totalTime,
            totalScore: resultData.maxScore,
            duration: resultData.totalTime
          }

          // 设置结果数据
          this.resultData = {
            totalScore: resultData.totalScore,
            precipitationScore: resultData.precipitationScore,
            weatherScore: resultData.weatherScore,
            ranking: resultData.ranking
          }

          // 设置用户答案数据
          if (resultData.userAnswer) {
            // 降水预报用户答案
            if (resultData.userAnswer.precipitationAnswer) {
              this.userPrecipitationData = resultData.userAnswer.precipitationAnswer.content || {}
            }

            // 天气预报用户答案
            if (resultData.userAnswer.weatherAnswer) {
              this.userWeatherAnswers = resultData.userAnswer.weatherAnswer.stations || {}
            }
          }

          // 设置标准答案数据 - 从scenarioData.answers获取
          if (scenarioData.answers) {
            // 天气预报标准答案从scenarioData.answers获取
            this.standardWeatherAnswers = scenarioData.answers || {}
          } else {
            // 如果scenarioData中没有答案，尝试从standardAnswer获取
            if (resultData.standardAnswer) {
              // 降水预报标准答案
              if (resultData.standardAnswer.precipitationAnswer) {
                this.standardPrecipitationData = resultData.standardAnswer.precipitationAnswer.content || {}
              }

              // 天气预报标准答案
              if (resultData.standardAnswer.weatherAnswer) {
                this.standardWeatherAnswers = resultData.standardAnswer.weatherAnswer.stations || {}
              }
            }
          }

          // 设置评分详情 - 使用gradingDetails数据
          if (resultData.gradingDetails && resultData.gradingDetails.elementScores) {
            // 从gradingDetails.elementScores获取第二部分评分详情
            const elementScores = resultData.gradingDetails.elementScores
            this.weatherScoring = {
              windForceScore: elementScores.windForce || 0,
              windDirectionScore: elementScores.windDirection || 0,
              // 分别保存最低和最高气温评分
              minTemperatureScore: elementScores.minTemperature || 0,
              maxTemperatureScore: elementScores.maxTemperature || 0,
              temperatureScore: (elementScores.minTemperature || 0) + (elementScores.maxTemperature || 0),
              precipitationScore: elementScores.precipitation || 0,
              disasterScore: elementScores.disasterWeather || 0
            }
          } else {
            // 如果没有gradingDetails，使用默认值
            this.weatherScoring = {
              windForceScore: 0,
              windDirectionScore: 0,
              minTemperatureScore: 0,
              maxTemperatureScore: 0,
              temperatureScore: 0,
              precipitationScore: 0,
              disasterScore: 0
            }
          }

          // 设置降水落区详细评分数据 - 根据您提供的数据结构调整
          if (resultData.gradingDetails && resultData.gradingDetails.detailResults) {
            const detailResults = resultData.gradingDetails.detailResults
            console.log('detailResults内容:', detailResults)

            // 查找降水落区评分数据 - 根据您的数据，precipitationArea应该在detailResults中
            if (detailResults.precipitationArea) {
              this.precipitationAreaDetails = detailResults.precipitationArea
              console.log('从gradingDetails.detailResults获取到precipitationArea:', this.precipitationAreaDetails)
            } else {
              // 如果没有precipitationArea，检查是否有precipitation相关的数据
              if (detailResults.precipitation) {
                this.precipitationAreaDetails = detailResults.precipitation
                console.log('从gradingDetails.detailResults获取到precipitation:', this.precipitationAreaDetails)
              } else {
                this.precipitationAreaDetails = null
                console.log('在detailResults中未找到precipitationArea或precipitation数据')
                console.log('detailResults的keys:', Object.keys(detailResults))
              }
            }
          } else if (resultData.gradingDetails && resultData.gradingDetails.precipitationArea) {
            // 尝试从gradingDetails直接获取
            this.precipitationAreaDetails = resultData.gradingDetails.precipitationArea
            console.log('从gradingDetails直接获取到precipitationArea:', this.precipitationAreaDetails)
          } else if (resultData.precipitationArea) {
            // 尝试从根级别获取
            this.precipitationAreaDetails = resultData.precipitationArea
            console.log('从根级别获取到precipitationArea:', this.precipitationAreaDetails)
          } else {
            this.precipitationAreaDetails = null
            console.log('未找到precipitationArea数据')
          }

          // 临时测试数据 - 根据您提供的数据结构创建
          if (!this.precipitationAreaDetails) {
            console.log('使用临时测试数据')
            this.precipitationAreaDetails = {
              score: 8.716981132075471,
              baseScores: {
                "中雨": 0.3,
                "大暴雨": 0.3,
                "大雨": 0.3,
                "小雨": 0,
                "暴雨": 0,
                "特大暴雨": 0.3
              },
              cmaMesoTSScores: {
                "中雨": 0,
                "大暴雨": 0,
                "大雨": 0,
                "小雨": 0.19047619047619047,
                "暴雨": 0.27358490566037735,
                "特大暴雨": 0
              },
              skillScores: {
                "中雨": 0.3,
                "大暴雨": 0.3,
                "大雨": 0.3,
                "小雨": 0,
                "暴雨": 0.07924528301886792,
                "特大暴雨": 0.3
              },
              studentTSScores: {
                "中雨": 0,
                "大暴雨": 0,
                "大雨": 0,
                "小雨": 0,
                "暴雨": 0.11320754716981132,
                "特大暴雨": 0
              },
              summary: "降水落区评分结果：\n最终得分：8.72分（满分40分）\n总站点数：106个\n\n各量级TS评分：\n  大暴雨：0.000\n  中雨：0.000"
            }
          }

          // 保留原有的评分详情逻辑作为备用
          if (resultData.scoringDetails) {
            this.precipitationScoring = resultData.scoringDetails.precipitation || {}
            this.weatherScoringResult = resultData.scoringDetails.weatherResult || {}
          }

          // 设置分析信息
          this.precipitationAnalysis = resultData.precipitationAnalysis || {}

          // 保存scenarioData供其他方法使用
          this.scenarioData = scenarioData
        } else {
          throw new Error(response.msg || '获取考试结果失败')
        }
      } catch (error) {
        console.error('获取考试结果失败:', error)
        throw error
      }
    },

    // 加载题目数据
    async loadQuestionData() {
      try {
        // 使用已保存的scenarioData，如果没有则重新获取
        let scenarioData = this.scenarioData || {}
        let examDetail = {}

        if (!this.scenarioData) {
          const response = await getWeatherExamDetail({ id: this.examId })

          if (response.code === 0 && response.data) {
            examDetail = response.data

            // 解析题目数据
            try {
              if (examDetail.scenarioData) {
                scenarioData = JSON.parse(examDetail.scenarioData)
              }
            } catch (e) {
              console.warn('解析scenarioData失败:', e)
            }
          }
        }

        this.questionData = {
          id: examDetail.questionId,
          title: examDetail.title || '历史个例天气预报',
          content: examDetail.content || '根据提供的气象观测资料，对指定站点进行24小时天气预报。',

          // 第一部分相关信息
          precipitationContent: scenarioData.precipitationContent || '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
          precipitationRegion: scenarioData.precipitationRegion || 'region1',

          // 站点信息 - 从scenarioData获取
          stations: JSON.stringify(scenarioData.stations || []),

          // 预报信息
          forecastDate: examDetail.forecastDate || scenarioData.forecastStartDate || '2035年9月9日',
          forecastTime: scenarioData.precipitationForecastTime || examDetail.forecastTime || '08时',

          totalScore: examDetail.totalScore || 100
        }
      } catch (error) {
        console.warn('获取题目详情失败，使用默认数据:', error)

        // 使用默认题目数据
        this.questionData = {
          title: '历史个例天气预报',
          content: '根据提供的气象观测资料，对指定站点进行24小时天气预报。',
          precipitationContent: '根据提供的气象资料，对指定区域进行24小时降水分级落区预报。请在地图上绘制不同等级的降水落区。',
          precipitationRegion: 'region1',
          forecastDate: '2035年9月9日',
          forecastTime: '08时',
          stations: JSON.stringify([]),
          totalScore: 100
        }
      }
    },

    // 获取区域标签
    getRegionLabel(regionValue) {
      const regionMap = {
        'region1': '区域一(华北区域)',
        'region2': '区域二(东北区域)',
        'region3': '区域三(长江中下游区域)',
        'region4': '区域四(华南区域)',
        'region5': '区域五(西南地区东部)',
        'region6': '区域六(青藏高原区域)',
        'region7': '区域七(新疆区域)',
        'region8': '区域八(西北地区东部区域)',
        'region9': '区域九(内蒙古区域)'
      }
      return regionMap[regionValue] || regionValue
    },

    // 格式化分数显示（3位小数）
    formatScore(score) {
      if (score === null || score === undefined) {
        return '0.000'
      }
      return Number(score).toFixed(3)
    },

    // 格式化分数显示（2位小数）
    formatScoreToTwoDecimals(score) {
      if (score === null || score === undefined) {
        return '0.00'
      }
      return Number(score).toFixed(2)
    },

    // 获取按照指定顺序排列的TS评分级别
    getSortedTSLevels(scoresObj) {
      if (!scoresObj || typeof scoresObj !== 'object') {
        return []
      }

      // 定义降水级别的显示顺序（取消晴雨，小雨开始）
      const levelOrder = ['小雨', '中雨', '大雨', '暴雨', '大暴雨', '特大暴雨']

      // 获取对象中存在的级别
      const availableLevels = Object.keys(scoresObj)

      // 按照预定义顺序过滤和排序
      const sortedLevels = levelOrder.filter(level => availableLevels.includes(level))

      // 添加任何不在预定义顺序中但存在于数据中的级别
      const remainingLevels = availableLevels.filter(level => !levelOrder.includes(level))

      return [...sortedLevels, ...remainingLevels]
    },

    // 获取风力评分详情说明
    getWindForceScoreDetail() {
      const score = this.weatherScoring.windForceScore || 0
      if (score >= 1.0) {
        return '完全匹配'
      } else if (score >= 0.8) {
        return '相邻等级'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取风向评分详情说明
    getWindDirectionScoreDetail() {
      const score = this.weatherScoring.windDirectionScore || 0
      if (score >= 1.0) {
        return '角度范围匹配'
      } else if (score >= 0.6) {
        return '相邻角度范围'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取降水评分详情说明
    getPrecipitationScoreDetail() {
      const score = this.weatherScoring.precipitationScore || 0
      if (score >= 2.0) {
        return '完全匹配'
      } else if (score >= 1.2) {
        return '相邻等级/特殊关系'
      } else if (score > 0) {
        return '部分正确'
      } else {
        return '预报错误'
      }
    },

    // 获取灾害天气评分详情说明
    getDisasterWeatherScoreDetail() {
      const score = this.weatherScoring.disasterScore || 0
      if (score >= 2.0) {
        return '完全匹配'
      } else if (score > 0) {
        return '部分匹配'
      } else {
        return '预报错误'
      }
    },

    // 返回考试列表
    goBack() {
      this.$router.push('/my/weather')
    }
  }
}
</script>

<style scoped>
.weather-exam-result {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
}

.weather-exam-result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.result-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  color: #2c3e50;
  padding: 30px;
  border-radius: 20px;
  margin-bottom: 25px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.result-title h2 {
  margin: 0 0 15px 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.exam-meta {
  display: flex;
  gap: 25px;
  align-items: center;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 25px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.meta-item i {
  font-size: 16px;
  color: #667eea;
}

.meta-item.score-display {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  border: 1px solid rgba(103, 194, 58, 0.3);
  font-weight: 600;
}

.meta-item.score-display i {
  color: #ffffff;
}

.final-score {
  font-size: 20px;
  font-weight: 800;
  margin: 0 4px;
}

.score-summary {
  flex-shrink: 0;
}

.score-card {
  display: flex;
  gap: 20px;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.score-item {
  text-align: center;
}

.score-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
  font-weight: 500;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 2px;
}

.score-value.primary { color: #409eff; }
.score-value.success { color: #67c23a; }
.score-value.info { color: #909399; }

.score-unit {
  font-size: 12px;
  color: #909399;
}

.score-divider {
  width: 1px;
  height: 40px;
  background: #e4e7ed;
}

.result-content {
  position: relative;
  z-index: 1;
}

.result-section {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
}

.section-header {
  padding: 20px 25px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.section-title h3 {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title h3::before {
  content: '📊';
  font-size: 24px;
}

.section-score .el-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(4px);
}

.precipitation-result-section,
.weather-result-section {
  padding: 25px;
}

.question-info {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  align-items: flex-start;
}

.question-content {
  flex: 2;
  padding: 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 16px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.1);
}

.question-content h4 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-content h4::before {
  content: '📝';
  font-size: 18px;
}

.question-content p {
  margin: 0;
  color: #37474f;
  line-height: 1.7;
  font-size: 14px;
  font-weight: 500;
}

.region-info {
  flex: 1;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 16px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  box-shadow: 0 4px 20px rgba(103, 194, 58, 0.1);
}

.region-info h4 {
  margin: 0 0 12px 0;
  color: #388e3c;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-info h4::before {
  content: '🗺️';
  font-size: 18px;
}

.precipitation-result-display {
  margin-top: 20px;
}

.result-tabs-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 15px;
}

.precipitation-display-container {
  margin-top: 15px;
  padding: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

.comparison-analysis {
  padding: 20px;
  background: linear-gradient(135deg, #fff9e6 0%, #fff3d3 100%);
  border-radius: 12px;
  margin-top: 15px;
}

.analysis-summary h4 {
  margin: 0 0 15px 0;
  color: #e6a23c;
  font-size: 18px;
  font-weight: 600;
}

.analysis-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

.analysis-label {
  font-weight: 600;
  color: #606266;
}

.analysis-value {
  color: #e6a23c;
  font-weight: 600;
}

.result-table-container {
  margin-top: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.scoring-details {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.scoring-breakdown {
  padding: 25px;
}

.scoring-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.scoring-part h4 {
  margin: 0 0 20px 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(25, 118, 210, 0.2);
}

.scoring-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scoring-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(25, 118, 210, 0.1);
  transition: all 0.3s ease;
}

.scoring-item:hover {
  background: rgba(25, 118, 210, 0.05);
  border-color: rgba(25, 118, 210, 0.2);
}

.item-name {
  font-weight: 500;
  color: #606266;
}

.item-score {
  font-weight: 600;
  color: #1976d2;
  font-size: 15px;
}

.item-detail {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  margin-left: 8px;
}

/* 评分要素信息样式 */
.scoring-elements-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.element-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.element-name {
  font-weight: 500;
  color: #606266;
  font-size: 13px;
}

.element-score {
  font-weight: 600;
  color: #1976d2;
  font-size: 13px;
}

/* 降水落区详细评分样式 */
.precipitation-area-details {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-section h5::before {
  content: '📊';
  font-size: 18px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.detail-label {
  font-weight: 500;
  color: #606266;
}

.detail-value {
  font-weight: 600;
  color: #1976d2;
}

.score-highlight {
  color: #e6a23c;
  font-size: 16px;
  font-weight: 700;
}

.ts-scores-section {
  margin-bottom: 20px;
}

.ts-scores-section h5 {
  margin: 0 0 15px 0;
  color: #67c23a;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ts-scores-section h5::before {
  content: '🎯';
  font-size: 18px;
}

.ts-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.ts-column {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.ts-column h6 {
  margin: 0 0 12px 0;
  color: #67c23a;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(103, 194, 58, 0.2);
}

.ts-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ts-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: rgba(103, 194, 58, 0.05);
  border-radius: 6px;
}

.ts-level {
  font-weight: 500;
  color: #606266;
  font-size: 13px;
}

.ts-score {
  font-weight: 600;
  color: #67c23a;
  font-size: 13px;
  font-family: 'Courier New', monospace;
}

.base-skill-scores {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.score-column {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(230, 162, 60, 0.2);
}

.score-column h6 {
  margin: 0 0 12px 0;
  color: #e6a23c;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(230, 162, 60, 0.2);
}

.score-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.score-item-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: rgba(230, 162, 60, 0.05);
  border-radius: 4px;
}

.score-level {
  font-weight: 500;
  color: #606266;
  font-size: 12px;
}

.score-value-small {
  font-weight: 600;
  color: #e6a23c;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.scoring-summary {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.scoring-summary h6 {
  margin: 0 0 10px 0;
  color: #1976d2;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.scoring-summary h6::before {
  content: '📝';
  font-size: 16px;
}

.summary-text {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 13px;
  background: rgba(25, 118, 210, 0.05);
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #1976d2;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
  position: relative;
  z-index: 1;
}

.result-actions .el-button {
  min-width: 140px;
  height: 45px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-actions .el-button:not(.el-button--primary) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.result-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .exam-meta {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .score-card {
    flex-direction: column;
    gap: 15px;
  }

  .score-divider {
    width: 100%;
    height: 1px;
  }

  .question-info {
    flex-direction: column;
    gap: 15px;
  }

  .scoring-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  /* 降水落区详细评分响应式 */
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .ts-comparison {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .base-skill-scores {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  /* 评分要素信息响应式 */
  .elements-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .element-info {
    padding: 6px 10px;
  }

  .element-name,
  .element-score {
    font-size: 12px;
  }

  .item-detail {
    display: block;
    margin-left: 0;
    margin-top: 4px;
    font-size: 11px;
  }
}
</style>
